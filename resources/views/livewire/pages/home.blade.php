<div class="mt-[127px] md:mt-[80px] mb-[73px]" id="main-content">
    <div class="flex-1 p-4 md:p-6 lg:p-10">
        <div class="mb-8 flex flex-wrap items-center justify-between gap-4">
            <div class="flex flex-col gap-2">
                <p class="font-semibold text-md md:text-lg">
                    {{ __('Select your cabinet') }}
                </p>
                <p class="text-sm">
                    {{ __('Choose the model to configure with the assortments of tools.') }}
                </p>
            </div>

            <div class="flex-1 basis-[450px] lg:grow-0">
                <div class="relative flex w-full items-center gap-2 rounded-full bg-sand-01 p-3">
                    <x-icons.search-line />
                    <input wire:model.live="search" placeholder="{{ __('Search..') }}" type="text"
                        class="w-full appearance-none bg-transparent text-sm text-neutral-10 placeholder:text-neutral-06 focus:outline-0 focus-visible:outline-none [&:not(:placeholder-shown)+*]:opacity-100">
                    <button wire:click="resetSearch" class="text-neutral-10 opacity-0 transition-opacity">
                        <div class="ri-xl leading-none">
                            <x-icons.close-line />
                        </div>
                    </button>
                </div>
            </div>
        </div>

        {{-- Categories Filters --}}
        <div class="my-8 flex flex-wrap items-center justify-between gap-4">
            <div class="flex flex-wrap items-center gap-2">
                <div class="relative inline-block">
                    <input wire:model.live="categories" value="" type="radio"
                        class="absolute inset-0 cursor-pointer appearance-none rounded-full opacity-0 [&:checked+*]:border-neutral-09 [&:checked+*]:bg-neutral-09 [&:checked+*]:text-white">
                    <div
                        class="inline-flex items-center rounded-full border border-neutral-03 px-3 py-1 text-neutral-08 transition-colors">
                        <span class="text-sm">
                            {{ __('All') }}
                        </span>
                    </div>
                </div>
                @foreach ($availableCategories as $category)
                    <div key="{{ $category->id }}" class="relative inline-block">
                        <input wire:model.live="categories" value="{{ $category->id }}" type="radio"
                            class="absolute inset-0 cursor-pointer appearance-none rounded-full opacity-0 [&:checked+*]:border-neutral-09 [&:checked+*]:bg-neutral-09 [&:checked+*]:text-white">
                        <div
                            class="inline-flex items-center rounded-full border border-neutral-03 px-3 py-1 text-neutral-08 transition-colors">
                            <span class="text-sm">
                                {{ $category->getNameLabelAttribute($lang) }}
                            </span>
                        </div>
                    </div>
                @endforeach
            </div>

            {{-- Show Featured --}}
            <div class="flex flex-wrap items-center gap-2">
                <div class="relative inline-block">
                    <input wire:model.live="featured" type="checkbox"
                        class="absolute z-10 inset-0 cursor-pointer appearance-none rounded-full opacity-0 [&:checked+*]:border-neutral-09 [&:checked+*]:bg-neutral-09 [&:checked+*]:text-white">
                    <div
                        class="inline-flex items-center rounded-full border border-neutral-03 px-3 py-1 text-neutral-08 transition-colors">
                        <span class="text-sm">
                            {{ __('Featured') }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        {{-- Products --}}
        <div class="mt-8 grid grid-cols-auto-fill-260 gap-x-4 gap-y-8">
            @if ($this->fetchProducts()->isEmpty())
                <p class="mt-4">{{ __('No results.') }}</p>
            @else
                @foreach ($this->fetchProducts() as $product)
                    @livewire(
                        'components.product-item',
                        [
                            'product' => $product,
                        ],
                        key($product->id)
                    )
                @endforeach
            @endif
        </div>
        {{-- <div class="mt-12">
            {{ $this->fetchProducts()->links('components.pagination') }}
        </div> --}}
    </div>
</div>
