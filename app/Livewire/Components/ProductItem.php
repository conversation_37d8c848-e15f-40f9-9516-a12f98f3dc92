<?php

namespace App\Livewire\Components;

use App\Models\Product;
use App\Models\Variant;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\App;

class ProductItem extends Component
{
    #[Session(key: 'lang')]
    public string $lang;
    
    public Product $product;
    public bool $isActive;

    public $selectedProduct;
    public $selectedVariant;

    public function mount(Product $product)
    {
        $this->lang = session('lang', App::getLocale());
        
        $this->product = $product;
    }

    public function render()
    {
        App::setLocale($this->lang);

        $this->isActive = $this->checkIfActive();
        return view('livewire.components.product-item');
    }

    #[On('selected-product.{product.id}')]
    public function setSelectedProduct(?Product $product = null, ?Variant $variant = null): void
    {
        $this->selectedProduct = $product;
        $this->selectedVariant = $variant;
    }

    #[On('clear-selected-product.{product.id}')]
    public function clearSelectedProduct(): void
    {
        $this->selectedProduct = null;
        $this->selectedVariant = null;
    }

    public function selectProduct(): void
    {
        $this->dispatch('select-product', $this->product);
    }

    public function checkIfActive(): bool
    {
        if ($this->selectedProduct && $this->selectedProduct->is($this->product)) {
            return true;
        }
        else {
            return false;
        }
    }
}
