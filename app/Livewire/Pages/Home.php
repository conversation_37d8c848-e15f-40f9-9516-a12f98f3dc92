<?php

namespace App\Livewire\Pages;

use App\Models\Product;
use App\Models\Variant;
use Livewire\Component;
use App\Models\Category;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\WithPagination;
use App\Models\Configuration;
use Livewire\Attributes\Session;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\App;
use Illuminate\Database\Eloquent\Builder;

class Home extends Component
{
    use WithPagination;

    #[Session(key: 'lang')]
    public string $lang;

    public string $title;

    public Configuration $config;

    #[Validate('required')]
    public ?Product $selectedProduct;

    #[Validate('required')]
    public ?Variant $selectedVariant;

    #[Url(as: 's', except: '')]
    public $search = '';

    #[Url(as: 'c', except: '')]
    public $categories = '';

    #[Url(as: 'f', except: false)]
    public $featured = false;

    public function mount()
    {
        $this->lang = session('lang', App::getLocale());

        $this->selectedProduct = null;
        $this->selectedVariant = null;

        $this->title = trans('Configurator - Beta Utensili');

        // $products = $this->fetchProducts();
    }

    public function render()
    {
        App::setLocale($this->lang);

        return view('livewire.pages.home', [
            'availableCategories' => Category::all(),
        ])->title($this->title);
    }

    public function updatedSearch()
    {
        $this->resetPage();
        // $products = $this->fetchProducts();
    }

    public function updatedCategories()
    {
        $this->resetPage();
        // $products = $this->fetchProducts();
    }

    public function updatedFeatured()
    {
        $this->resetPage();
        // $products = $this->fetchProducts();
    }

    public function resetSearch()
    {
        $this->search = '';
        // $products = $this->fetchProducts();
    }

    #[On('create-config')]
    public function createConfig(): void
    {
        $this->validate();
        $this->config = Configuration::create([
            'uuid' => (string) Str::uuid(),
            'product_id' => $this->selectedProduct->id,
            'variant_id' => $this->selectedVariant->id,
        ]);
        $this->redirect(route('type-assortments', ['uuid' => $this->config->uuid]), navigate: true);
    }

    #[On('select-product')]
    public function selectProduct(Product $product): void
    {
        // Clear the previous selected product and variant
        if (isset($this->selectedProduct)) {
            $this->dispatch("clear-selected-product.{$this->selectedProduct->id}");
        }
        
        if (isset($this->selectedVariant)) {
            $this->dispatch("clear-selected-variant.{$this->selectedVariant->id}");
        }

        // Set the selected product and variant
        $this->selectedProduct = $product;
        $this->selectedVariant = $product->variants->first();

        $this->dispatch('set-next-button-status', true);
        $this->dispatch("selected-product.{$product->id}", $this->selectedProduct, $this->selectedVariant);
        $this->dispatch("selected-variant.{$this->selectedVariant->id}", $this->selectedProduct, $this->selectedVariant);
    }

    #[On('select-variant')]
    public function selectVariant(Product $product, Variant $variant): void
    {
        // Clear the previous selected product and variant
        if (isset($this->selectedProduct)) {
            $this->dispatch("clear-selected-product.{$this->selectedProduct->id}");
        }
        
        if (isset($this->selectedVariant)) {
            $this->dispatch("clear-selected-variant.{$this->selectedVariant->id}");
        }

        // Set the selected product and variant
        $this->selectedProduct = $product;
        $this->selectedVariant = $variant;

        $this->dispatch('set-next-button-status', true);
        $this->dispatch("selected-variant.{$variant->id}", $this->selectedProduct, $this->selectedVariant);
        $this->dispatch("selected-product.{$product->id}", $this->selectedProduct, $this->selectedVariant);
    }

    #[Computed]
    private function fetchProducts()
    {
        return Product::query()
            ->when($this->search, function (Builder $query) {
                return $query->search($this->search);
            })
            ->when($this->categories, function (Builder $query) {
                return $query->where('category_id', $this->categories);
            })
            ->when($this->featured, function (Builder $query) {
                return $query->orWhere('featured', true);
            })
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
